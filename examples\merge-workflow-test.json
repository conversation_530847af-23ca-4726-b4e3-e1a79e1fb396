{"name": "使用Merge的工作流连线测试", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "trigger", "name": "手动触发"}, {"parameters": {"values": {"string": [{"name": "testData", "value": "处理数据"}]}}, "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [200, -100], "id": "data-node", "name": "数据节点"}, {"parameters": {"values": {"string": [{"name": "workflow", "value": "{\"name\":\"测试子工作流\",\"nodes\":[{\"id\":\"set1\",\"type\":\"n8n-nodes-base.set\",\"parameters\":{\"values\":{\"string\":[{\"name\":\"result\",\"value\":\"子工作流执行成功!\"}]}},\"position\":[0,0]}],\"connections\":{}}"}]}}, "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [200, 100], "id": "workflow-def", "name": "工作流定义"}, {"parameters": {"mode": "passThrough"}, "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [400, 0], "id": "merge", "name": "合并数据"}, {"parameters": {"workflowSource": "workflow", "workflowDefinitionField": "workflow", "workflowDefinitionFormat": "n8n", "dataMode": "each", "concurrency": 1}, "type": "CUSTOM.parallelWorkflowExecutor", "typeVersion": 1, "position": [600, 0], "id": "executor", "name": "并行执行器"}], "connections": {"手动触发": {"main": [[{"node": "数据节点", "type": "main", "index": 0}, {"node": "工作流定义", "type": "main", "index": 0}]]}, "数据节点": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "工作流定义": {"main": [[{"node": "并行执行器", "type": "main", "index": 1}]]}, "合并数据": {"main": [[{"node": "并行执行器", "type": "main", "index": 0}]]}}}