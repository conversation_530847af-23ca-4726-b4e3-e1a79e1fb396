{"name": "简单工作流连线测试", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "trigger", "name": "手动触发"}, {"parameters": {"values": {"string": [{"name": "data1", "value": "测试数据1"}, {"name": "data2", "value": "测试数据2"}]}}, "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [200, 0], "id": "data-source", "name": "数据源"}, {"parameters": {"workflowSource": "fixed", "workflowId": {"mode": "id", "value": "1"}, "concurrency": 2, "dataMode": "each"}, "type": "CUSTOM.parallelWorkflowExecutor", "typeVersion": 1, "position": [400, 0], "id": "executor", "name": "并行执行器"}], "connections": {"手动触发": {"main": [[{"node": "数据源", "type": "main", "index": 0}]]}, "数据源": {"main": [[{"node": "并行执行器", "type": "main", "index": 0}]]}}}