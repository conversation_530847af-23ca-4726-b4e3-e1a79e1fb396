/**
 * 工作流数据存储节点测试
 * 验证get/set操作和数据共享功能
 */

// 模拟工作流数据存储功能
class MockWorkflowDataStore {
    constructor() {
        this.stores = new Map();
        this.cleanupCallbacks = new Map();
    }

    // 模拟设置数据
    set(workflowId, key, value, metadata = {}) {
        if (!this.stores.has(workflowId)) {
            this.stores.set(workflowId, new Map());
        }
        
        const store = this.stores.get(workflowId);
        const dataItem = {
            value,
            createdAt: metadata.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            nodeId: metadata.nodeId || 'test-node',
            nodeName: metadata.nodeName || 'Test Node',
        };

        // 如果键已存在，保留创建时间
        if (store.has(key)) {
            const existingItem = store.get(key);
            dataItem.createdAt = existingItem.createdAt;
        }

        store.set(key, dataItem);
        return {
            operation: 'set',
            key,
            success: true,
            message: `数据已成功存储到键 '${key}'`,
        };
    }

    // 模拟获取数据
    get(workflowId, key, defaultValue = null) {
        const result = {
            operation: 'get',
            key,
        };

        if (this.stores.has(workflowId)) {
            const store = this.stores.get(workflowId);
            if (store.has(key)) {
                const dataItem = store.get(key);
                result.value = dataItem.value;
                result.success = true;
                result.found = true;
                result.metadata = {
                    createdAt: dataItem.createdAt,
                    updatedAt: dataItem.updatedAt,
                    nodeId: dataItem.nodeId,
                    nodeName: dataItem.nodeName,
                };
                return result;
            }
        }

        result.value = defaultValue;
        result.success = true;
        result.found = false;
        result.message = `键 '${key}' 不存在，返回默认值`;
        return result;
    }

    // 模拟删除数据
    delete(workflowId, key) {
        let existed = false;
        
        if (this.stores.has(workflowId)) {
            const store = this.stores.get(workflowId);
            existed = store.has(key);
            if (existed) {
                store.delete(key);
            }
        }

        return {
            operation: 'delete',
            key,
            success: true,
            existed,
            message: existed ? `键 '${key}' 已删除` : `键 '${key}' 不存在`,
        };
    }

    // 模拟列出所有键
    list(workflowId) {
        const result = {
            operation: 'list',
            keys: [],
            count: 0,
            success: true,
            items: {},
        };

        if (this.stores.has(workflowId)) {
            const store = this.stores.get(workflowId);
            result.keys = Array.from(store.keys());
            result.count = result.keys.length;
            
            for (const key of result.keys) {
                const dataItem = store.get(key);
                result.items[key] = {
                    createdAt: dataItem.createdAt,
                    updatedAt: dataItem.updatedAt,
                    nodeId: dataItem.nodeId,
                    nodeName: dataItem.nodeName,
                };
            }
        }

        return result;
    }

    // 模拟清空存储
    clear(workflowId) {
        let count = 0;
        
        if (this.stores.has(workflowId)) {
            const store = this.stores.get(workflowId);
            count = store.size;
            store.clear();
        }

        return {
            operation: 'clear',
            success: true,
            clearedCount: count,
            message: `已清空 ${count} 个数据项`,
        };
    }

    // 模拟清理工作流数据
    cleanup(workflowId) {
        const existed = this.stores.has(workflowId);
        if (existed) {
            this.stores.delete(workflowId);
        }
        return existed;
    }

    // 获取统计信息
    getStats() {
        const stats = {
            totalStores: this.stores.size,
            stores: [],
        };

        for (const [workflowId, store] of this.stores) {
            stats.stores.push({
                workflowId,
                itemCount: store.size,
                keys: Array.from(store.keys()),
            });
        }

        return stats;
    }
}

// 测试数据
const testWorkflowId = 'test-workflow-123';
const dataStore = new MockWorkflowDataStore();

console.log("=== 工作流数据存储功能测试 ===\n");

// 测试1: 设置数据
console.log("测试1: 设置数据");
const setResult1 = dataStore.set(testWorkflowId, 'userInfo', {
    name: '张三',
    age: 30,
    department: '技术部'
});
console.log("✅ 设置用户信息:", JSON.stringify(setResult1, null, 2));

const setResult2 = dataStore.set(testWorkflowId, 'config', {
    timeout: 5000,
    retries: 3,
    debug: true
});
console.log("✅ 设置配置信息:", JSON.stringify(setResult2, null, 2));

// 测试2: 获取数据
console.log("\n测试2: 获取数据");
const getResult1 = dataStore.get(testWorkflowId, 'userInfo');
console.log("✅ 获取用户信息:", JSON.stringify(getResult1, null, 2));

const getResult2 = dataStore.get(testWorkflowId, 'nonexistent', { default: 'value' });
console.log("✅ 获取不存在的键:", JSON.stringify(getResult2, null, 2));

// 测试3: 更新数据
console.log("\n测试3: 更新数据");
// 等待一小段时间以确保更新时间不同
setTimeout(() => {
    const updateResult = dataStore.set(testWorkflowId, 'userInfo', {
        name: '张三',
        age: 31, // 年龄更新
        department: '技术部',
        lastLogin: new Date().toISOString()
    });
    console.log("✅ 更新用户信息:", JSON.stringify(updateResult, null, 2));

    // 验证更新后的数据
    const updatedData = dataStore.get(testWorkflowId, 'userInfo');
    console.log("✅ 验证更新后的数据:", JSON.stringify(updatedData, null, 2));

    // 测试4: 列出所有键
    console.log("\n测试4: 列出所有键");
    const listResult = dataStore.list(testWorkflowId);
    console.log("✅ 列出所有键:", JSON.stringify(listResult, null, 2));

    // 测试5: 删除数据
    console.log("\n测试5: 删除数据");
    const deleteResult1 = dataStore.delete(testWorkflowId, 'config');
    console.log("✅ 删除配置信息:", JSON.stringify(deleteResult1, null, 2));

    const deleteResult2 = dataStore.delete(testWorkflowId, 'nonexistent');
    console.log("✅ 删除不存在的键:", JSON.stringify(deleteResult2, null, 2));

    // 测试6: 验证删除后的状态
    console.log("\n测试6: 验证删除后的状态");
    const listAfterDelete = dataStore.list(testWorkflowId);
    console.log("✅ 删除后的键列表:", JSON.stringify(listAfterDelete, null, 2));

    // 测试7: 多工作流数据隔离
    console.log("\n测试7: 多工作流数据隔离");
    const workflow2Id = 'test-workflow-456';
    dataStore.set(workflow2Id, 'data1', 'workflow2-value1');
    dataStore.set(workflow2Id, 'data2', 'workflow2-value2');

    const stats = dataStore.getStats();
    console.log("✅ 多工作流统计信息:", JSON.stringify(stats, null, 2));

    // 测试8: 清空存储
    console.log("\n测试8: 清空存储");
    const clearResult = dataStore.clear(testWorkflowId);
    console.log("✅ 清空工作流1存储:", JSON.stringify(clearResult, null, 2));

    const statsAfterClear = dataStore.getStats();
    console.log("✅ 清空后的统计信息:", JSON.stringify(statsAfterClear, null, 2));

    // 测试9: 工作流清理
    console.log("\n测试9: 工作流清理");
    const cleanupResult1 = dataStore.cleanup(testWorkflowId);
    const cleanupResult2 = dataStore.cleanup(workflow2Id);
    console.log(`✅ 清理工作流1: ${cleanupResult1}`);
    console.log(`✅ 清理工作流2: ${cleanupResult2}`);

    const finalStats = dataStore.getStats();
    console.log("✅ 最终统计信息:", JSON.stringify(finalStats, null, 2));

    console.log("\n=== 测试完成 ===");

    // 性能测试
    console.log("\n=== 性能测试 ===");
    const perfWorkflowId = 'perf-test-workflow';
    const startTime = Date.now();
    
    // 批量设置数据
    for (let i = 0; i < 1000; i++) {
        dataStore.set(perfWorkflowId, `key_${i}`, {
            index: i,
            data: `value_${i}`,
            timestamp: new Date().toISOString()
        });
    }
    
    const setTime = Date.now();
    console.log(`✅ 设置1000个数据项耗时: ${setTime - startTime}ms`);
    
    // 批量获取数据
    for (let i = 0; i < 1000; i++) {
        dataStore.get(perfWorkflowId, `key_${i}`);
    }
    
    const getTime = Date.now();
    console.log(`✅ 获取1000个数据项耗时: ${getTime - setTime}ms`);
    
    // 清理性能测试数据
    dataStore.cleanup(perfWorkflowId);
    console.log("✅ 性能测试数据已清理");
    
    console.log("\n=== 所有测试完成 ===");
}, 100);
