{"name": "工作流数据存储示例", "nodes": [{"parameters": {"values": {"string": [{"name": "userId", "value": "user123"}, {"name": "action", "value": "login"}]}, "options": {}}, "id": "start-data", "name": "初始数据", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [240, 300]}, {"parameters": {"operation": "set", "key": "userSession", "value": {"userId": "{{ $json.userId }}", "loginTime": "{{ new Date().toISOString() }}", "sessionId": "{{ Math.random().toString(36).substr(2, 9) }}", "status": "active"}, "scope": "workflow", "includeMetadata": true}, "id": "store-session", "name": "存储用户会话", "type": "workflowDataStore", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"values": {"string": [{"name": "step", "value": "processing"}, {"name": "data", "value": "some processing data"}]}, "options": {}}, "id": "processing-step", "name": "处理步骤", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [680, 300]}, {"parameters": {"operation": "get", "key": "userSession", "defaultValue": {}, "scope": "workflow", "includeMetadata": true}, "id": "get-session", "name": "获取用户会话", "type": "workflowDataStore", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"operation": "set", "key": "processResult", "value": {"userId": "{{ $('获取用户会话').item.json.value.userId }}", "sessionId": "{{ $('获取用户会话').item.json.value.sessionId }}", "processedAt": "{{ new Date().toISOString() }}", "result": "success", "data": "{{ $json.data }}"}, "scope": "workflow"}, "id": "store-result", "name": "存储处理结果", "type": "workflowDataStore", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"values": {"string": [{"name": "step", "value": "validation"}]}, "options": {}}, "id": "validation-step", "name": "验证步骤", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [680, 480]}, {"parameters": {"operation": "get", "key": "userSession", "scope": "workflow", "includeMetadata": true}, "id": "get-session-2", "name": "再次获取会话", "type": "workflowDataStore", "typeVersion": 1, "position": [900, 480]}, {"parameters": {"operation": "set", "key": "validationResult", "value": {"userId": "{{ $('再次获取会话').item.json.value.userId }}", "sessionId": "{{ $('再次获取会话').item.json.value.sessionId }}", "validatedAt": "{{ new Date().toISOString() }}", "status": "validated"}, "scope": "workflow"}, "id": "store-validation", "name": "存储验证结果", "type": "workflowDataStore", "typeVersion": 1, "position": [1120, 480]}, {"parameters": {}, "id": "merge-results", "name": "合并结果", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [1340, 390]}, {"parameters": {"operation": "list", "scope": "workflow", "includeMetadata": true}, "id": "list-all-data", "name": "列出所有数据", "type": "workflowDataStore", "typeVersion": 1, "position": [1560, 390]}, {"parameters": {"values": {"object": [{"name": "summary", "value": {"totalKeys": "{{ $json.count }}", "keys": "{{ $json.keys }}", "userSession": "{{ $json.items.userSession }}", "processResult": "{{ $json.items.processResult }}", "validationResult": "{{ $json.items.validationResult }}"}}]}, "options": {}}, "id": "create-summary", "name": "创建摘要", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [1780, 390]}, {"parameters": {"operation": "clear", "scope": "workflow"}, "id": "cleanup-data", "name": "清理数据", "type": "workflowDataStore", "typeVersion": 1, "position": [2000, 390]}], "connections": {"初始数据": {"main": [[{"node": "存储用户会话", "type": "main", "index": 0}]]}, "存储用户会话": {"main": [[{"node": "处理步骤", "type": "main", "index": 0}, {"node": "验证步骤", "type": "main", "index": 0}]]}, "处理步骤": {"main": [[{"node": "获取用户会话", "type": "main", "index": 0}]]}, "获取用户会话": {"main": [[{"node": "存储处理结果", "type": "main", "index": 0}]]}, "存储处理结果": {"main": [[{"node": "合并结果", "type": "main", "index": 0}]]}, "验证步骤": {"main": [[{"node": "再次获取会话", "type": "main", "index": 0}]]}, "再次获取会话": {"main": [[{"node": "存储验证结果", "type": "main", "index": 0}]]}, "存储验证结果": {"main": [[{"node": "合并结果", "type": "main", "index": 1}]]}, "合并结果": {"main": [[{"node": "列出所有数据", "type": "main", "index": 0}]]}, "列出所有数据": {"main": [[{"node": "创建摘要", "type": "main", "index": 0}]]}, "创建摘要": {"main": [[{"node": "清理数据", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2024-12-29T00:00:00.000Z", "versionId": "1"}