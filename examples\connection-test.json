{"name": "连线测试工作流", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "trigger", "name": "手动触发"}, {"parameters": {"values": {"string": [{"name": "testData", "value": "测试数据"}]}}, "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [200, 0], "id": "data-node", "name": "数据节点"}, {"parameters": {"values": {"string": [{"name": "workflow", "value": "{\"name\":\"测试工作流\",\"nodes\":[{\"id\":\"test\",\"type\":\"n8n-nodes-base.set\",\"parameters\":{\"values\":{\"string\":[{\"name\":\"result\",\"value\":\"成功!\"}]}}}],\"connections\":{}}"}]}}, "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [200, 200], "id": "workflow-node", "name": "工作流定义节点"}, {"parameters": {"workflowSource": "workflow", "workflowDefinitionField": "workflow", "workflowDefinitionFormat": "n8n"}, "type": "CUSTOM.parallelWorkflowExecutor", "typeVersion": 1, "position": [400, 100], "id": "executor", "name": "并行执行器"}], "connections": {"手动触发": {"main": [[{"node": "数据节点", "type": "main", "index": 0}, {"node": "工作流定义节点", "type": "main", "index": 0}]]}, "数据节点": {"main": [[{"node": "并行执行器", "type": "main", "index": 0}]]}, "工作流定义节点": {"main": [[{"node": "并行执行器", "type": "main", "index": 1}]]}}}