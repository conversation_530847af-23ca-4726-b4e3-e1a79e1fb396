{"name": "并行工作流执行器连线示例", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "trigger", "name": "手动触发"}, {"parameters": {"values": {"string": [{"name": "data", "value": "处理数据1"}]}}, "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [200, 0], "id": "data-source", "name": "数据源"}, {"parameters": {"values": {"string": [{"name": "workflow", "value": "{\"name\":\"动态子工作流\",\"nodes\":[{\"id\":\"set-node\",\"type\":\"n8n-nodes-base.set\",\"typeVersion\":1,\"position\":[0,0],\"parameters\":{\"values\":{\"string\":[{\"name\":\"result\",\"value\":\"Hello from dynamic workflow!\"}]}}}],\"connections\":{}}"}]}}, "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [200, 200], "id": "workflow-definition", "name": "工作流定义"}, {"parameters": {"workflowSource": "workflow", "workflowDefinitionField": "workflow", "workflowDefinitionFormat": "n8n", "dataMode": "each", "concurrency": 2}, "type": "CUSTOM.parallelWorkflowExecutor", "typeVersion": 1, "position": [400, 100], "id": "parallel-executor", "name": "并行工作流执行器"}], "connections": {"手动触发": {"main": [[{"node": "数据源", "type": "main", "index": 0}, {"node": "工作流定义", "type": "main", "index": 0}]]}, "数据源": {"main": [[{"node": "并行工作流执行器", "type": "main", "index": 0}]]}, "工作流定义": {"main": [[{"node": "并行工作流执行器", "type": "main", "index": 1}]]}}, "active": false, "settings": {"executionOrder": "v1"}}