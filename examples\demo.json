{"name": "My workflow", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "659a2c09-4955-4fb0-ad98-7c7abdcb5c40", "name": "When clicking ‘Test workflow’"}, {"parameters": {"key": "userInfo", "value": "{\"name\": \"ymm\"}", "scope": "execution"}, "type": "CUSTOM.workflowDataStore", "typeVersion": 1, "position": [220, 0], "id": "9d6bccbf-7101-4a16-ad78-37c2cecad950", "name": "工作流数据存储"}, {"parameters": {"operation": "list", "scope": "execution"}, "type": "CUSTOM.workflowDataStore", "typeVersion": 1, "position": [440, 0], "id": "e291e73a-1120-41f6-b26d-c5ae2a833925", "name": "工作流数据存储1"}, {"parameters": {"operation": "get", "key": "userInfo", "defaultValue": "{}", "scope": "execution"}, "type": "CUSTOM.workflowDataStore", "typeVersion": 1, "position": [660, 0], "id": "1ee6dd47-4080-43fe-aa65-47450ac0029f", "name": "工作流数据存储2"}, {"parameters": {"workflowSource": "workflow"}, "type": "CUSTOM.parallelWorkflowExecutor", "typeVersion": 1, "position": [860, 80], "id": "99bd0bae-17b8-4866-94b1-a599997ea7ee", "name": "并行工作流执行器"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "工作流数据存储", "type": "main", "index": 0}]]}, "工作流数据存储": {"main": [[{"node": "工作流数据存储1", "type": "main", "index": 0}]]}, "工作流数据存储1": {"main": [[{"node": "工作流数据存储2", "type": "main", "index": 0}]]}, "工作流数据存储2": {"main": [[{"node": "并行工作流执行器", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "8fb1fab7-7b8f-4a25-b464-8489b4ccd88b", "meta": {"instanceId": "67e8bfca6e69d053bf915df24c00f6c443685fb57067432780966786c274f1d7"}, "id": "0QgHcpKGCVOXzkU6", "tags": []}